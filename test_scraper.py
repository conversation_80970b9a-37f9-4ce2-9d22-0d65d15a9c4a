#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 简化版本的问卷星数据抓取
"""

import asyncio
import csv
import logging
from datetime import datetime
from playwright.async_api import async_playwright


async def test_wjx_scraping():
    """测试问卷星页面抓取"""
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    url = "https://www.wjx.cn/resultquery.aspx?activity=228250516"
    queries = ["2503040030138", "888888"]
    results = []
    
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(
            executable_path="/Applications/Chromium.app/Contents/MacOS/Chromium",
            headless=False,  # 可视化模式，方便调试
            args=['--no-sandbox', '--disable-web-security']
        )
        
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        
        page = await context.new_page()
        
        try:
            # 访问页面
            logger.info(f"访问页面: {url}")
            await page.goto(url, wait_until='networkidle')
            await page.wait_for_timeout(3000)
            
            # 获取页面标题
            title = await page.title()
            logger.info(f"页面标题: {title}")
            
            # 截图保存初始页面
            await page.screenshot(path="initial_page.png")
            
            for query in queries:
                logger.info(f"测试查询参数: {query}")
                
                try:
                    # 查找输入框 - 尝试多种选择器
                    input_selectors = [
                        'input[type="text"]',
                        'input[name*="pwd"]',
                        'input[id*="pwd"]',
                        'input[placeholder*="密码"]',
                        'input[placeholder*="查询"]',
                        '#txtPwd',
                        '#pwd'
                    ]
                    
                    input_found = False
                    for selector in input_selectors:
                        try:
                            input_element = await page.wait_for_selector(selector, timeout=2000)
                            if input_element:
                                logger.info(f"找到输入框: {selector}")
                                
                                # 清空并输入查询参数
                                await input_element.fill("")
                                await input_element.fill(query)
                                await page.wait_for_timeout(1000)
                                
                                input_found = True
                                break
                        except:
                            continue
                    
                    if not input_found:
                        # 如果找不到特定输入框，获取所有输入框
                        all_inputs = await page.query_selector_all('input')
                        logger.info(f"页面共有 {len(all_inputs)} 个输入框")
                        
                        for i, inp in enumerate(all_inputs):
                            input_type = await inp.get_attribute('type')
                            input_name = await inp.get_attribute('name')
                            input_id = await inp.get_attribute('id')
                            placeholder = await inp.get_attribute('placeholder')
                            
                            logger.info(f"输入框 {i}: type={input_type}, name={input_name}, id={input_id}, placeholder={placeholder}")
                            
                            # 尝试使用第一个文本输入框
                            if input_type == 'text' or input_type is None:
                                await inp.fill("")
                                await inp.fill(query)
                                await page.wait_for_timeout(1000)
                                input_found = True
                                break
                    
                    if not input_found:
                        logger.error("未找到合适的输入框")
                        continue
                    
                    # 查找提交按钮
                    button_selectors = [
                        'button[type="submit"]',
                        'input[type="submit"]',
                        'button:has-text("查询")',
                        'button:has-text("提交")',
                        'input[value*="查询"]',
                        '#btnQuery',
                        '.btn-query'
                    ]
                    
                    button_found = False
                    for selector in button_selectors:
                        try:
                            button = await page.wait_for_selector(selector, timeout=2000)
                            if button:
                                logger.info(f"找到提交按钮: {selector}")
                                await button.click()
                                button_found = True
                                break
                        except:
                            continue
                    
                    if not button_found:
                        # 尝试按回车键
                        await page.keyboard.press('Enter')
                        logger.info("使用回车键提交")
                    
                    # 等待结果加载
                    await page.wait_for_timeout(3000)
                    
                    # 截图保存结果页面
                    await page.screenshot(path=f"result_{query}.png")
                    
                    # 获取页面内容
                    page_text = await page.inner_text('body')
                    
                    # 检查是否有结果
                    has_results = True
                    no_result_indicators = ['没有找到', '无结果', '未找到', '暂无数据', '查询不到']
                    for indicator in no_result_indicators:
                        if indicator in page_text:
                            has_results = False
                            break
                    
                    # 尝试提取表格数据
                    tables_data = []
                    tables = await page.query_selector_all('table')
                    for table in tables:
                        rows = await table.query_selector_all('tr')
                        table_data = []
                        for row in rows:
                            cells = await row.query_selector_all('td, th')
                            row_data = []
                            for cell in cells:
                                cell_text = await cell.inner_text()
                                row_data.append(cell_text.strip())
                            if row_data:
                                table_data.append(row_data)
                        if table_data:
                            tables_data.append(table_data)
                    
                    # 保存结果
                    result = {
                        'query': query,
                        'success': has_results,
                        'timestamp': datetime.now().isoformat(),
                        'page_title': await page.title(),
                        'current_url': page.url,
                        'page_text': page_text[:1000],  # 截取前1000字符
                        'tables': tables_data
                    }
                    
                    results.append(result)
                    logger.info(f"查询 {query} 完成，结果: {'有数据' if has_results else '无数据'}")
                    
                    # 返回初始页面
                    await page.goto(url, wait_until='networkidle')
                    await page.wait_for_timeout(2000)
                    
                except Exception as e:
                    logger.error(f"处理查询 {query} 时出错: {e}")
                    results.append({
                        'query': query,
                        'success': False,
                        'error': str(e),
                        'timestamp': datetime.now().isoformat()
                    })
        
        finally:
            await browser.close()
    
    # 保存结果到CSV
    if results:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"wjx_test_results_{timestamp}.csv"
        
        with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = ['query', 'success', 'timestamp', 'page_title', 'current_url', 'page_text', 'error']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for result in results:
                # 处理表格数据
                if 'tables' in result and result['tables']:
                    result['tables_summary'] = f"找到 {len(result['tables'])} 个表格"
                
                writer.writerow({
                    'query': result.get('query', ''),
                    'success': result.get('success', False),
                    'timestamp': result.get('timestamp', ''),
                    'page_title': result.get('page_title', ''),
                    'current_url': result.get('current_url', ''),
                    'page_text': result.get('page_text', ''),
                    'error': result.get('error', '')
                })
        
        print(f"结果已保存到: {csv_filename}")
        
        # 打印结果摘要
        print("\n测试结果摘要:")
        for result in results:
            query = result.get('query', 'Unknown')
            success = result.get('success', False)
            status = "成功" if success else "失败"
            print(f"  查询 '{query}': {status}")
            if 'error' in result:
                print(f"    错误: {result['error']}")


if __name__ == "__main__":
    print("开始测试问卷星数据抓取...")
    asyncio.run(test_wjx_scraping())
