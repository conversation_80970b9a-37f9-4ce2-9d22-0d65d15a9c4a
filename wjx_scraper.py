#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问卷星数据抓取脚本
使用Playwright模拟浏览器操作，获取结构化数据并导出到CSV文件
"""

import asyncio
import csv
import json
import logging
import os
import time
from datetime import datetime
from typing import Dict, List, Optional, Any

import pandas as pd
from playwright.async_api import async_playwright, Page, Browser, BrowserContext


class WJXScraper:
    """问卷星数据抓取器"""
    
    def __init__(self, chromium_path: str = "/Applications/Chromium.app"):
        """
        初始化抓取器
        
        Args:
            chromium_path: Chromium浏览器路径
        """
        self.chromium_path = chromium_path
        self.base_url = "https://www.wjx.cn/resultquery.aspx?activity=228250516"
        self.results = []
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('wjx_scraper.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    async def init_browser(self) -> tuple[Browser, BrowserContext, Page]:
        """初始化浏览器"""
        self.logger.info("正在启动浏览器...")
        
        playwright = await async_playwright().start()
        
        # 启动Chromium浏览器
        browser = await playwright.chromium.launch(
            executable_path=f"{self.chromium_path}/Contents/MacOS/Chromium",
            headless=False,  # 设置为True可以无头模式运行
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        )
        
        # 创建浏览器上下文
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        # 创建新页面
        page = await context.new_page()
        
        self.logger.info("浏览器启动成功")
        return browser, context, page
    
    async def navigate_to_page(self, page: Page) -> bool:
        """导航到目标页面"""
        try:
            self.logger.info(f"正在访问页面: {self.base_url}")
            await page.goto(self.base_url, wait_until='networkidle')
            await page.wait_for_timeout(2000)  # 等待页面完全加载
            
            # 检查页面是否正确加载
            title = await page.title()
            self.logger.info(f"页面标题: {title}")
            
            return True
        except Exception as e:
            self.logger.error(f"访问页面失败: {e}")
            return False
    
    async def search_with_query(self, page: Page, query: str) -> Dict[str, Any]:
        """
        使用查询参数进行搜索
        
        Args:
            page: 页面对象
            query: 查询参数
            
        Returns:
            包含查询结果的字典
        """
        try:
            self.logger.info(f"正在搜索查询参数: {query}")
            
            # 查找输入框并输入查询参数
            # 常见的输入框选择器
            input_selectors = [
                'input[type="text"]',
                'input[name*="query"]',
                'input[name*="search"]',
                'input[id*="query"]',
                'input[id*="search"]',
                'input[placeholder*="查询"]',
                'input[placeholder*="搜索"]',
                '#txtQuery',
                '#query',
                '.search-input'
            ]
            
            input_element = None
            for selector in input_selectors:
                try:
                    input_element = await page.wait_for_selector(selector, timeout=3000)
                    if input_element:
                        self.logger.info(f"找到输入框: {selector}")
                        break
                except:
                    continue
            
            if not input_element:
                # 如果找不到输入框，尝试获取页面所有输入框
                all_inputs = await page.query_selector_all('input')
                if all_inputs:
                    input_element = all_inputs[0]  # 使用第一个输入框
                    self.logger.info("使用第一个找到的输入框")
            
            if not input_element:
                raise Exception("未找到输入框")
            
            # 清空输入框并输入查询参数
            await input_element.fill("")
            await input_element.fill(query)
            await page.wait_for_timeout(1000)
            
            # 查找并点击搜索按钮
            search_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("查询")',
                'button:has-text("搜索")',
                'input[value*="查询"]',
                'input[value*="搜索"]',
                '#btnQuery',
                '#search',
                '.search-btn'
            ]
            
            search_button = None
            for selector in search_selectors:
                try:
                    search_button = await page.wait_for_selector(selector, timeout=3000)
                    if search_button:
                        self.logger.info(f"找到搜索按钮: {selector}")
                        break
                except:
                    continue
            
            if search_button:
                await search_button.click()
            else:
                # 如果找不到按钮，尝试按回车键
                await input_element.press('Enter')
                self.logger.info("使用回车键提交搜索")
            
            # 等待搜索结果加载
            await page.wait_for_timeout(3000)
            
            # 获取搜索结果
            result_data = await self.extract_result_data(page, query)
            
            return result_data
            
        except Exception as e:
            self.logger.error(f"搜索查询参数 {query} 时出错: {e}")
            return {
                'query': query,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def extract_result_data(self, page: Page, query: str) -> Dict[str, Any]:
        """
        提取页面结果数据
        
        Args:
            page: 页面对象
            query: 查询参数
            
        Returns:
            提取的数据字典
        """
        try:
            # 等待页面加载完成
            await page.wait_for_timeout(2000)
            
            # 获取页面内容
            page_content = await page.content()
            
            # 检查是否有结果
            has_results = await self.check_has_results(page)
            
            result_data = {
                'query': query,
                'success': has_results,
                'timestamp': datetime.now().isoformat(),
                'page_title': await page.title(),
                'current_url': page.url
            }
            
            if has_results:
                # 提取具体的结果数据
                extracted_data = await self.extract_specific_data(page)
                result_data.update(extracted_data)
                self.logger.info(f"成功提取查询 {query} 的结果数据")
            else:
                result_data['message'] = '未找到结果'
                self.logger.info(f"查询 {query} 未找到结果")
            
            return result_data
            
        except Exception as e:
            self.logger.error(f"提取结果数据时出错: {e}")
            return {
                'query': query,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def check_has_results(self, page: Page) -> bool:
        """检查页面是否有搜索结果"""
        try:
            # 常见的"无结果"提示文本
            no_result_texts = [
                '没有找到',
                '无结果',
                '未找到',
                '暂无数据',
                '查询不到',
                '无记录'
            ]
            
            page_text = await page.inner_text('body')
            
            # 检查是否包含"无结果"的提示
            for text in no_result_texts:
                if text in page_text:
                    return False
            
            # 检查是否有结果表格或列表
            result_selectors = [
                'table',
                '.result',
                '.data-table',
                '.list',
                '[class*="result"]',
                '[id*="result"]'
            ]
            
            for selector in result_selectors:
                elements = await page.query_selector_all(selector)
                if elements:
                    return True
            
            return True  # 默认认为有结果
            
        except Exception as e:
            self.logger.error(f"检查结果状态时出错: {e}")
            return False
    
    async def extract_specific_data(self, page: Page) -> Dict[str, Any]:
        """提取页面中的具体数据"""
        try:
            data = {}
            
            # 尝试提取表格数据
            tables = await page.query_selector_all('table')
            if tables:
                table_data = []
                for i, table in enumerate(tables):
                    rows = await table.query_selector_all('tr')
                    table_rows = []
                    for row in rows:
                        cells = await row.query_selector_all('td, th')
                        row_data = []
                        for cell in cells:
                            cell_text = await cell.inner_text()
                            row_data.append(cell_text.strip())
                        if row_data:
                            table_rows.append(row_data)
                    if table_rows:
                        table_data.append(table_rows)
                
                if table_data:
                    data['tables'] = table_data
            
            # 提取所有文本内容
            body_text = await page.inner_text('body')
            data['page_text'] = body_text
            
            # 尝试提取特定的数据字段
            # 这里可以根据实际页面结构添加更多的提取逻辑
            
            return data

        except Exception as e:
            self.logger.error(f"提取具体数据时出错: {e}")
            return {}

    async def run_scraping(self, queries: List[str]) -> List[Dict[str, Any]]:
        """
        运行数据抓取

        Args:
            queries: 查询参数列表

        Returns:
            抓取结果列表
        """
        browser = None
        try:
            browser, context, page = await self.init_browser()

            # 导航到目标页面
            if not await self.navigate_to_page(page):
                raise Exception("无法访问目标页面")

            results = []

            for query in queries:
                self.logger.info(f"开始处理查询: {query}")

                # 执行搜索
                result = await self.search_with_query(page, query)
                results.append(result)

                # 等待一段时间避免请求过快
                await page.wait_for_timeout(2000)

                # 返回到初始页面准备下一次搜索
                await page.goto(self.base_url, wait_until='networkidle')
                await page.wait_for_timeout(1000)

            self.results = results
            return results

        except Exception as e:
            self.logger.error(f"抓取过程中出错: {e}")
            return []
        finally:
            if browser:
                await browser.close()

    def save_to_csv(self, filename: str = None) -> str:
        """
        将结果保存到CSV文件

        Args:
            filename: 输出文件名，如果为None则自动生成

        Returns:
            保存的文件路径
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"wjx_results_{timestamp}.csv"

        try:
            # 准备CSV数据
            csv_data = []

            for result in self.results:
                row = {
                    '查询参数': result.get('query', ''),
                    '查询成功': '是' if result.get('success', False) else '否',
                    '查询时间': result.get('timestamp', ''),
                    '页面标题': result.get('page_title', ''),
                    '当前URL': result.get('current_url', ''),
                    '错误信息': result.get('error', ''),
                    '消息': result.get('message', '')
                }

                # 如果有表格数据，添加到行中
                if 'tables' in result:
                    tables = result['tables']
                    for i, table in enumerate(tables):
                        for j, table_row in enumerate(table):
                            row[f'表格{i+1}_行{j+1}'] = ' | '.join(table_row)

                # 添加页面文本（截取前500字符）
                if 'page_text' in result:
                    row['页面内容'] = result['page_text'][:500] + '...' if len(result['page_text']) > 500 else result['page_text']

                csv_data.append(row)

            # 保存到CSV文件
            if csv_data:
                df = pd.DataFrame(csv_data)
                df.to_csv(filename, index=False, encoding='utf-8-sig')
                self.logger.info(f"结果已保存到: {filename}")
            else:
                self.logger.warning("没有数据可保存")

            return filename

        except Exception as e:
            self.logger.error(f"保存CSV文件时出错: {e}")
            return ""

    def save_to_json(self, filename: str = None) -> str:
        """
        将结果保存到JSON文件

        Args:
            filename: 输出文件名，如果为None则自动生成

        Returns:
            保存的文件路径
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"wjx_results_{timestamp}.json"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)

            self.logger.info(f"结果已保存到: {filename}")
            return filename

        except Exception as e:
            self.logger.error(f"保存JSON文件时出错: {e}")
            return ""


async def main():
    """主函数"""
    # 查询参数列表
    queries = [
        "2503040030138",  # 正确查询参数，有结果
        "888888"          # 错误查询参数，无结果
    ]

    # 创建抓取器实例
    scraper = WJXScraper()

    print("开始抓取问卷星数据...")
    print(f"目标页面: {scraper.base_url}")
    print(f"查询参数: {queries}")
    print("-" * 50)

    # 运行抓取
    results = await scraper.run_scraping(queries)

    if results:
        print(f"\n抓取完成！共获取 {len(results)} 条结果")

        # 保存结果
        csv_file = scraper.save_to_csv()
        json_file = scraper.save_to_json()

        print(f"CSV文件: {csv_file}")
        print(f"JSON文件: {json_file}")

        # 显示结果摘要
        print("\n结果摘要:")
        for result in results:
            query = result.get('query', 'Unknown')
            success = result.get('success', False)
            status = "成功" if success else "失败"
            print(f"  查询 '{query}': {status}")
            if not success and 'error' in result:
                print(f"    错误: {result['error']}")
    else:
        print("抓取失败，未获取到任何结果")


if __name__ == "__main__":
    asyncio.run(main())
