#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行脚本 - 问卷星数据抓取
"""

import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from wjx_scraper import WJXScraper


def main():
    """主函数"""
    print("=" * 60)
    print("问卷星数据抓取工具")
    print("=" * 60)
    
    # 检查Chromium路径
    chromium_path = "/Applications/Chromium.app"
    if not os.path.exists(chromium_path):
        print(f"错误: 未找到Chromium浏览器，请确保已安装到: {chromium_path}")
        print("或者修改脚本中的chromium_path参数")
        return
    
    print(f"使用Chromium路径: {chromium_path}")
    
    # 查询参数
    queries = [
        "2503040030138",  # 正确查询参数，有结果
        "888888"          # 错误查询参数，无结果
    ]
    
    print(f"查询参数: {queries}")
    print("-" * 60)
    
    # 创建抓取器
    scraper = WJXScraper(chromium_path=chromium_path)
    
    # 运行抓取
    try:
        results = asyncio.run(scraper.run_scraping(queries))
        
        if results:
            print(f"\n✅ 抓取完成！共获取 {len(results)} 条结果")
            
            # 保存结果
            csv_file = scraper.save_to_csv()
            json_file = scraper.save_to_json()
            
            if csv_file:
                print(f"📊 CSV文件: {csv_file}")
            if json_file:
                print(f"📄 JSON文件: {json_file}")
            
            # 显示结果摘要
            print("\n📋 结果摘要:")
            for result in results:
                query = result.get('query', 'Unknown')
                success = result.get('success', False)
                status = "✅ 成功" if success else "❌ 失败"
                print(f"  查询 '{query}': {status}")
                
                if not success and 'error' in result:
                    print(f"    错误: {result['error']}")
                elif success and 'message' in result:
                    print(f"    信息: {result['message']}")
        else:
            print("❌ 抓取失败，未获取到任何结果")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"❌ 运行出错: {e}")
    
    print("\n" + "=" * 60)
    print("程序结束")


if __name__ == "__main__":
    main()
