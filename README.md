# 问卷星数据抓取工具

这是一个使用Python和Playwright自动化抓取问卷星页面数据的工具。

## 功能特性

- 🚀 使用Playwright自动化浏览器操作
- 📊 支持多个查询参数批量处理
- 💾 自动导出结果到CSV和JSON文件
- 📝 详细的日志记录
- 🔍 智能识别页面元素（输入框、按钮等）
- 📸 自动截图保存页面状态

## 环境要求

- Python 3.8+
- Chromium浏览器（安装在 `/Applications/Chromium.app`）
- 虚拟环境（推荐）

## 安装步骤

1. **创建并激活虚拟环境**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # macOS/Linux
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **确保Chromium浏览器已安装**
   - 下载并安装Chromium到 `/Applications/Chromium.app`
   - 或者修改脚本中的浏览器路径

## 使用方法

### 方法1: 使用主脚本

```bash
python run.py
```

### 方法2: 使用测试脚本

```bash
python test_scraper.py
```

### 方法3: 直接使用WJXScraper类

```python
import asyncio
from wjx_scraper import WJXScraper

async def main():
    scraper = WJXScraper()
    queries = ["2503040030138", "888888"]
    results = await scraper.run_scraping(queries)
    
    # 保存结果
    scraper.save_to_csv()
    scraper.save_to_json()

asyncio.run(main())
```

## 配置说明

### 查询参数

在脚本中修改查询参数列表：

```python
queries = [
    "2503040030138",  # 正确查询参数，有结果
    "888888"          # 错误查询参数，无结果
]
```

### 浏览器设置

如果Chromium安装在其他位置，修改路径：

```python
scraper = WJXScraper(chromium_path="/path/to/your/chromium")
```

### 运行模式

- **可视化模式**: `headless=False` - 可以看到浏览器操作过程
- **无头模式**: `headless=True` - 后台运行，不显示浏览器窗口

## 输出文件

运行后会生成以下文件：

- `wjx_results_YYYYMMDD_HHMMSS.csv` - CSV格式的结果数据
- `wjx_results_YYYYMMDD_HHMMSS.json` - JSON格式的原始数据
- `wjx_scraper.log` - 详细的运行日志
- `initial_page.png` - 初始页面截图
- `result_[query].png` - 每个查询结果的截图

## CSV输出字段

| 字段名 | 说明 |
|--------|------|
| 查询参数 | 输入的查询参数 |
| 查询成功 | 是否成功获取结果 |
| 查询时间 | 查询执行时间 |
| 页面标题 | 结果页面标题 |
| 当前URL | 结果页面URL |
| 错误信息 | 如果失败，显示错误信息 |
| 页面内容 | 页面文本内容（截取） |
| 表格数据 | 提取的表格数据 |

## 故障排除

### 1. 找不到Chromium浏览器

```
错误: 未找到Chromium浏览器
```

**解决方案**: 
- 确保Chromium已安装到 `/Applications/Chromium.app`
- 或修改脚本中的 `chromium_path` 参数

### 2. 找不到页面元素

```
错误: 未找到输入框
```

**解决方案**:
- 检查目标网站是否有变化
- 运行测试脚本查看页面结构
- 修改元素选择器

### 3. 网络连接问题

```
错误: 访问页面失败
```

**解决方案**:
- 检查网络连接
- 确认目标URL是否正确
- 增加等待时间

## 自定义开发

### 添加新的数据提取逻辑

在 `extract_specific_data` 方法中添加：

```python
async def extract_specific_data(self, page: Page) -> Dict[str, Any]:
    data = {}
    
    # 添加自定义提取逻辑
    custom_elements = await page.query_selector_all('.custom-class')
    if custom_elements:
        data['custom_data'] = [await elem.inner_text() for elem in custom_elements]
    
    return data
```

### 修改页面元素选择器

在相应方法中修改选择器列表：

```python
input_selectors = [
    'input[type="text"]',
    'input[name*="query"]',
    # 添加新的选择器
    '.your-custom-selector'
]
```

## 注意事项

1. **遵守网站使用条款**: 请确保您的使用符合目标网站的服务条款
2. **请求频率**: 脚本已内置延时，避免过于频繁的请求
3. **数据准确性**: 请验证抓取的数据准确性
4. **法律合规**: 确保数据抓取行为符合相关法律法规

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个工具。
